/**
 * Experience Service
 *
 * Business logic for experience operations
 */
const experienceRepository = require('@models/repositories/experience.repository');
const { ApiException } = require('@utils/exception.utils');
const { HttpStatus } = require('@utils/enums.utils');
const { EXPERIENCE } = require('@utils/messages.utils');

/**
 * Experience service
 */
const experienceService = {
  /**
   * Create a new experience with weeks, insights, and media
   * @param {Object} data - Experience data
   * @param {string} userId - Creator user ID
   * @returns {Promise<Object>} Created experience
   */
  createExperience: async (data, userId) => {
    try {
      const experienceData = {
        ...data,
        createdBy: userId,
      };

      return await experienceRepository.create(experienceData);
    } catch (error) {
      throw error;
    }
  },

  /**
   * Get all experiences with pagination
   * @param {Object} options - Query options
   * @param {number} options.page - Page number
   * @param {number} options.limit - Items per page
   * @param {string} options.createdBy - Filter by creator ID
   * @param {string} options.userId - User ID for enrollment check
   * @returns {Promise<Object>} Experiences with pagination
   */
  getAllExperiences: async (options) => {
    try {
      const { page, limit, createdBy, userId } = options;

      return await experienceRepository.findAll({
        page,
        limit,
        createdBy,
        userId,
      });
    } catch (error) {
      throw error;
    }
  },

  /**
   * Get experience by ID with all associations
   * @param {string} id - Experience ID
   * @param {string} userId - User ID for enrollment check
   * @returns {Promise<Object>} Experience with associations
   */
  getExperienceById: async (id, userId) => {
    try {
      const experience = await experienceRepository.findById(id, true, userId);

      if (!experience) {
        throw new ApiException(HttpStatus.NOT_FOUND, EXPERIENCE.NOT_FOUND);
      }

      return experience;
    } catch (error) {
      throw error;
    }
  },
};

module.exports = experienceService;
