/**
 * Follow Model
 * Represents the many-to-many relationship between User and User for following
 */
const { Model, DataTypes } = require('sequelize');

class Follow extends Model {
  static init(sequelize) {
    super.init(
      {
        id: {
          type: DataTypes.UUID,
          defaultValue: DataTypes.UUIDV4,
          primaryKey: true,
        },
        followerId: {
          type: DataTypes.UUID,
          allowNull: false,
          references: {
            model: 'User',
            key: 'id',
          },
          onDelete: 'CASCADE',
        },
        followingId: {
          type: DataTypes.UUID,
          allowNull: false,
          references: {
            model: 'User',
            key: 'id',
          },
          onDelete: 'CASCADE',
        },
      },
      {
        sequelize,
        modelName: 'Follow',
        tableName: 'Follow',
        timestamps: true,
        indexes: [
          {
            unique: true,
            fields: ['followerId', 'followingId'],
            name: 'follow_unique_idx',
          },
        ],
      }
    );
  }

  static associate(models) {
    // No direct associations needed as this is a junction table
    // The associations are defined in the User model
  }
}

module.exports = Follow;
