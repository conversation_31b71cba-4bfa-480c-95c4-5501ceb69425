/**
 * OnboardingConfig Controller
 *
 * Handles onboarding config-related HTTP requests
 */
const onboardingConfigService = require('./onboarding-config.service');
const { ApiResponse } = require('@utils/response.utils');
const { ONBOARDING_CONFIG } = require('@utils/messages.utils');

/**
 * OnboardingConfig controller
 */
const onboardingConfigController = {
  /**
   * Get all onboarding configs with pagination and optional search
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  getAllConfigs: async (req, res, next) => {
    try {
      // Extract query parameters
      const { page = 1, limit = 10, search = '' } = req.query;

      // Get configs with pagination
      const result = await onboardingConfigService.getAllConfigs({
        page: parseInt(page, 10),
        limit: parseInt(limit, 10),
        search,
      });

      return ApiResponse.success(
        res,
        ONBOARDING_CONFIG.ALL_RETRIEVED,
        result.configs,
        {
          pagination: result.pagination,
        }
      );
    } catch (error) {
      next(error);
    }
  },

  /**
   * Get onboarding config by ID
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  getConfigById: async (req, res, next) => {
    try {
      const { id } = req.params;
      const config = await onboardingConfigService.getConfigById(id);
      return ApiResponse.success(res, ONBOARDING_CONFIG.RETRIEVED, config);
    } catch (error) {
      next(error);
    }
  },

  /**
   * Create a new onboarding config
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  createConfig: async (req, res, next) => {
    try {
      const { imageUrl, title, description } = req.body;
      const config = await onboardingConfigService.createConfig({
        imageUrl,
        title,
        description,
      });
      return ApiResponse.created(res, ONBOARDING_CONFIG.CREATED, config);
    } catch (error) {
      next(error);
    }
  },

  /**
   * Update onboarding config
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  updateConfig: async (req, res, next) => {
    try {
      const { id } = req.params;
      const { imageUrl, title, description } = req.body;
      const config = await onboardingConfigService.updateConfig(id, {
        imageUrl,
        title,
        description,
      });
      return ApiResponse.success(res, ONBOARDING_CONFIG.UPDATED, config);
    } catch (error) {
      next(error);
    }
  },

  /**
   * Delete onboarding config
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  deleteConfig: async (req, res, next) => {
    try {
      const { id } = req.params;
      await onboardingConfigService.deleteConfig(id);
      return ApiResponse.success(res, ONBOARDING_CONFIG.DELETED);
    } catch (error) {
      next(error);
    }
  },
};

module.exports = onboardingConfigController;
