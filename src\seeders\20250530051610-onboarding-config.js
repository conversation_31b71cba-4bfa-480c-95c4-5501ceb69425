'use strict';

const { v4: uuidv4 } = require('uuid');

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    // Check if any onboarding config exists
    const existingConfigs = await queryInterface.sequelize.query(
      'SELECT COUNT(*) as count FROM "OnboardingConfig"',
      { type: queryInterface.sequelize.QueryTypes.SELECT }
    );

    // Only insert if no configs exist
    if (existingConfigs[0].count === '0') {
      await queryInterface.bulkInsert('OnboardingConfig', [
        {
          id: uuidv4(),
          imageUrl: null,
          title: 'Welcome!',
          description:
            "Teaching is tough but it's not supposed to feel isolating. At WTD, you'll find tools, support, and a community that gets it. Whether you're here to grow, connect, or just catch your breath, this is your place to start.\n\nNo pressure. No perfection. Just real support for real teachers.",
          createdAt: new Date(),
          updatedAt: new Date(),
        },
      ]);
    }
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.bulkDelete('OnboardingConfig', {
      title: 'Welcome!',
    });
  },
};
