/**
 * Admin Insight Validation
 *
 * Validation schemas for insight-related endpoints
 */
const { body, param, query } = require('express-validator');
const { InsightStatus } = require('@utils/enums.utils');

/**
 * Validation schemas for insight endpoints
 */
const insightValidation = {
  /**
   * Validate get all insights request
   */
  getAll: [
    query('page')
      .optional()
      .isInt({ min: 1 })
      .withMessage('Page must be a positive integer'),
    query('limit')
      .optional()
      .isInt({ min: 1, max: 100 })
      .withMessage('Limit must be between 1 and 100'),
    query('search')
      .optional()
      .isString()
      .withMessage('Search must be a string'),
    query('status')
      .optional()
      .isIn(InsightStatus.values)
      .withMessage(`Status must be one of: ${InsightStatus.values.join(', ')}`),
  ],

  /**
   * Validate get insight by ID request
   */
  getById: [param('id').isUUID(4).withMessage('Invalid insight ID')],

  /**
   * Validate update insight request
   */
  update: [
    param('id').isUUID(4).withMessage('Invalid insight ID'),
    body('insightText')
      .optional()
      .isString()
      .notEmpty()
      .withMessage('Insight text cannot be empty')
      .isLength({ max: 250 })
      .withMessage('Insight text must be at most 250 characters'),
    body('sourceUrl')
      .optional()
      .isURL()
      .withMessage('Source URL must be a valid URL'),
    body('pdCategoryId')
      .optional()
      .isUUID(4)
      .withMessage('Invalid PD category ID'),
    body('focusIds')
      .optional()
      .isArray()
      .withMessage('Focus IDs must be an array'),
    body('focusIds.*').optional().isUUID(4).withMessage('Invalid focus ID'),
    body('wtdCategoryIds')
      .optional()
      .isArray()
      .withMessage('WTD category IDs must be an array'),
    body('wtdCategoryIds.*')
      .optional()
      .isUUID(4)
      .withMessage('Invalid WTD category ID'),
  ],

  /**
   * Validate delete insight request
   */
  delete: [param('id').isUUID(4).withMessage('Invalid insight ID')],

  /**
   * Validate update insight status request
   */
  updateStatus: [
    param('id').isUUID(4).withMessage('Invalid insight ID'),
    body('status')
      .isIn([InsightStatus.APPROVED, InsightStatus.REJECTED])
      .withMessage(
        `Status must be either ${InsightStatus.APPROVED} or ${InsightStatus.REJECTED}`
      ),
    body('rejectionReason')
      .if(body('status').equals(InsightStatus.REJECTED))
      .isString()
      .notEmpty()
      .withMessage('Rejection reason is required when rejecting an insight'),
  ],

  /**
   * Validate approve insight request
   * @deprecated Use updateStatus instead
   */
  approve: [param('id').isUUID(4).withMessage('Invalid insight ID')],

  /**
   * Validate reject insight request
   * @deprecated Use updateStatus instead
   */
  reject: [
    param('id').isUUID(4).withMessage('Invalid insight ID'),
    body('rejectionReason')
      .optional()
      .isString()
      .withMessage('Rejection reason must be a string'),
  ],

  /**
   * Validate delete contribution request
   */
  deleteContribution: [
    param('contributionId').isUUID(4).withMessage('Invalid contribution ID'),
  ],
};

module.exports = insightValidation;
