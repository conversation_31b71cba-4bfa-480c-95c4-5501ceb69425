/**
 * Follow Repository
 *
 * Handles data access operations for the Follow model
 */
const { ApiException } = require('@utils/exception.utils');
const databaseService = require('@config/database.config');
const { FOLLOW } = require('@utils/messages.utils');
const { Sequelize } = require('sequelize');
const { Op } = Sequelize;

/**
 * Base Repository class with common functionality
 */
class BaseRepository {
  constructor() {
    this.models = {
      Follow: databaseService.getFollowModel(),
      User: databaseService.getUserModel(),
    };
  }

  _getUserAttributes() {
    return {
      exclude: ['password'],
      include: [
        [
          databaseService
            .getSequelize()
            .literal(
              `(SELECT COUNT(*)::INTEGER FROM "Follow" WHERE "Follow"."followingId" = "User"."id")`
            ),
          'followersCount',
        ],
        [
          databaseService
            .getSequelize()
            .literal(
              `(SELECT COUNT(*)::INTEGER FROM "Follow" WHERE "Follow"."followerId" = "User"."id")`
            ),
          'followingCount',
        ],
      ],
    };
  }
}

/**
 * Helper class for Follow operations
 */
class FollowHelper extends BaseRepository {
  /**
   * Validate users exist and are not the same
   */
  async validateUsers(followerId, followingId, transaction) {
    if (followerId === followingId) {
      throw new ApiException(400, FOLLOW.CANNOT_FOLLOW_SELF);
    }

    const users = await this.models.User.findAll({
      where: {
        id: { [Op.in]: [followerId, followingId] },
      },
      attributes: this._getUserAttributes(),
      transaction,
    });

    if (users.length !== 2) {
      throw new ApiException(404, FOLLOW.NOT_FOUND);
    }

    return users;
  }

  /**
   * Get updated user data with follow counts
   */
  async getUpdatedUserData(followerId, followingId) {
    const [follower, following] = await Promise.all([
      this.models.User.findByPk(followerId, {
        attributes: this._getUserAttributes(),
      }),
      this.models.User.findByPk(followingId, {
        attributes: this._getUserAttributes(),
      }),
    ]);

    return { follower, following };
  }

  /**
   * Handle follow/unfollow operation
   */
  async handleFollowOperation(followerId, followingId, transaction) {
    const existingFollow = await this.models.Follow.findOne({
      where: {
        followerId,
        followingId,
      },
      transaction,
    });

    if (existingFollow) {
      await existingFollow.destroy({ transaction });
      return false;
    }

    await this.models.Follow.create(
      {
        followerId,
        followingId,
      },
      { transaction }
    );
    return true;
  }
}

/**
 * Main Follow Repository class
 */
class FollowRepository extends BaseRepository {
  constructor() {
    super();
    this.helper = new FollowHelper();
  }

  /**
   * Toggle follow status for a user
   * @param {string} followerId - User ID of the follower
   * @param {string} followingId - User ID of the user being followed
   * @returns {Promise<Object>} Object containing follow status and user data
   * @throws {ApiException} If user not found or trying to follow self
   */
  async toggleFollow(followerId, followingId) {
    const transaction = await databaseService.getSequelize().transaction();

    try {
      // Validate users
      await this.helper.validateUsers(followerId, followingId, transaction);

      // Handle follow operation
      const isFollowing = await this.helper.handleFollowOperation(
        followerId,
        followingId,
        transaction
      );

      // Commit transaction
      await transaction.commit();

      // Get updated user data
      const { follower, following } = await this.helper.getUpdatedUserData(
        followerId,
        followingId
      );

      return {
        isFollowing,
        follower,
        following,
      };
    } catch (error) {
      await transaction.rollback();
      console.error('Error in toggleFollow repository:', error);
      throw error;
    }
  }

  /**
   * Check if a user is following another user
   * @param {string} followerId - User ID of the follower
   * @param {string} followingId - User ID of the user being followed
   * @returns {Promise<boolean>} True if following
   */
  async isFollowing(followerId, followingId) {
    try {
      const follow = await this.models.Follow.findOne({
        where: {
          followerId,
          followingId,
        },
      });

      return !!follow;
    } catch (error) {
      console.error('Error in isFollowing repository:', error);
      throw error;
    }
  }
}

module.exports = new FollowRepository();
