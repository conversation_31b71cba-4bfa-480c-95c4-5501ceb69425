'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('WtdIceBreaker', {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.INTEGER,
      },
      userId: {
        type: Sequelize.UUID,
        allowNull: false,
        references: {
          model: 'User',
          key: 'id',
        },
        onDelete: 'CASCADE',
      },
      teachSubject: {
        type: Sequelize.STRING,
        allowNull: true,
      },
      contentIn3Words: {
        type: Sequelize.STRING,
        allowNull: true,
      },
      inspiration: {
        type: Sequelize.TEXT,
        allowNull: true,
      },
      funFact: {
        type: Sequelize.TEXT,
        allowNull: true,
      },
      magicButton: {
        type: Sequelize.STRING,
        allowNull: true,
      },
      resourcesEmojis: {
        type: Sequelize.STRING,
        allowNull: true,
      },
      themeSong: {
        type: Sequelize.STRING,
        allowNull: true,
      },
      impactTeacher: {
        type: Sequelize.TEXT,
        allowNull: true,
      },
      isPrivate: {
        type: Sequelize.BOOLEAN,
        defaultValue: false,
      },
      createdAt: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.NOW,
      },
      updatedAt: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.NOW,
      },
    });

    await queryInterface.addIndex('WtdIceBreaker', ['userId']);
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('WtdIceBreaker');
  },
};
