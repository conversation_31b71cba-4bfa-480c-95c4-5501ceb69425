/**
 * OnboardingConfig Validation
 *
 * Validation schemas for onboarding config operations
 */
const { body, param, query } = require('express-validator');

/**
 * OnboardingConfig validation schemas
 */
const onboardingConfigValidation = {
  /**
   * Get all configs validation schema
   */
  getAll: [
    query('page')
      .optional()
      .isInt({ min: 1 })
      .withMessage('Page must be a positive integer')
      .toInt(),
    query('limit')
      .optional()
      .isInt({ min: 1, max: 100 })
      .withMessage('Limit must be between 1 and 100')
      .toInt(),
  ],

  /**
   * Get config by ID validation schema
   */
  getById: [param('id').isUUID().withMessage('Invalid config ID')],

  /**
   * Create config validation schema
   */
  create: [
    body('imageUrl')
      .optional()
      .isString()
      .withMessage('Image URL must be a string'),
    body('title').optional().isString().withMessage('Title must be a string'),
    body('description')
      .optional()
      .isString()
      .withMessage('Description must be a string'),
  ],

  /**
   * Update config validation schema
   */
  update: [
    param('id').isUUID().withMessage('Invalid config ID'),
    body('imageUrl')
      .optional()
      .isString()
      .withMessage('Image URL must be a string'),
    body('title').optional().isString().withMessage('Title must be a string'),
    body('description')
      .optional()
      .isString()
      .withMessage('Description must be a string'),
  ],

  /**
   * Delete config validation schema
   */
  delete: [param('id').isUUID().withMessage('Invalid config ID')],
};

module.exports = onboardingConfigValidation;
