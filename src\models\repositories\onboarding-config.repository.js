/**
 * OnboardingConfig Repository
 * Handles database operations for onboarding config
 */
const { ApiException } = require('@utils/exception.utils');
const { PAGINATION } = require('@utils/constants');
const databaseService = require('@config/database.config');
const { Sequelize } = require('sequelize');
const { Op } = Sequelize;
const commonRepository = require('./common.repository');
const { ONBOARDING_CONFIG } = require('@utils/messages.utils');
const { HttpStatus } = require('@utils/enums.utils');

/**
 * Base Repository class with common functionality
 */
class BaseRepository {
  constructor() {
    this.models = {
      OnboardingConfig: databaseService.getOnboardingConfigModel(),
    };
  }
}

/**
 * Main OnboardingConfig Repository class
 */
class OnboardingConfigRepository extends BaseRepository {
  constructor() {
    super();
  }

  /**
   * Find all onboarding configs with pagination
   * @param {Object} options - Query options
   * @param {number} options.page - Page number (1-based)
   * @param {number} options.limit - Number of items per page
   * @returns {Promise<Object>} Object containing configs and pagination info
   */
  async findAll({
    page = PAGINATION.DEFAULT_PAGE,
    limit = PAGINATION.DEFAULT_LIMIT,
  } = {}) {
    try {
      const offset = commonRepository.calculateOffset(page, limit);

      const { count, rows } =
        await this.models.OnboardingConfig.findAndCountAll({
          limit,
          offset,
          order: [['createdAt', 'DESC']],
        });

      return {
        configs: rows,
        pagination: commonRepository.buildPaginationInfo(count, page, limit),
      };
    } catch (error) {
      console.error('Error in findAll repository:', error);
      throw error;
    }
  }

  /**
   * Find onboarding config by ID
   * @param {string} id - Onboarding config UUID
   * @returns {Promise<Object>} Onboarding config instance
   * @throws {ApiException} If config not found
   */
  async findById(id) {
    try {
      const config = await this.models.OnboardingConfig.findByPk(id);

      if (!config) {
        throw new ApiException(
          HttpStatus.NOT_FOUND,
          ONBOARDING_CONFIG.NOT_FOUND
        );
      }

      return config;
    } catch (error) {
      console.error('Error in findById repository:', error);
      throw error;
    }
  }

  /**
   * Create a new onboarding config
   * @param {Object} data - Onboarding config data
   * @param {string} data.title - The title
   * @param {string} data.description - The description
   * @param {string} data.imageUrl - Optional image URL
   * @returns {Promise<Object>} Created onboarding config
   */
  async create(data) {
    const transaction = await databaseService.getSequelize().transaction();

    try {
      const config = await this.models.OnboardingConfig.create(
        {
          title: data.title,
          description: data.description,
          imageUrl: data.imageUrl,
        },
        { transaction }
      );

      await transaction.commit();
      return await this.findById(config.id);
    } catch (error) {
      await transaction.rollback();
      console.error('Error in create repository:', error);
      throw error;
    }
  }

  /**
   * Update onboarding config
   * @param {string} id - Onboarding config UUID
   * @param {Object} data - Data to update
   * @param {string} [data.title] - Updated title
   * @param {string} [data.description] - Updated description
   * @param {string} [data.imageUrl] - Updated image URL
   * @returns {Promise<Object>} Updated onboarding config
   * @throws {ApiException} If config not found
   */
  async update(id, data) {
    const transaction = await databaseService.getSequelize().transaction();

    try {
      const config = await this.findById(id);
      Object.assign(config, data);
      await config.save({ transaction });

      await transaction.commit();
      return await this.findById(config.id);
    } catch (error) {
      await transaction.rollback();
      console.error('Error in update repository:', error);
      throw error;
    }
  }

  /**
   * Delete onboarding config
   * @param {string} id - Onboarding config UUID
   * @returns {Promise<boolean>} True if deleted
   * @throws {ApiException} If config not found
   */
  async delete(id) {
    try {
      const config = await this.findById(id);
      await config.destroy();
      return true;
    } catch (error) {
      console.error('Error in delete repository:', error);
      throw error;
    }
  }
}

module.exports = new OnboardingConfigRepository();
