/**
 * Admin Insight Module
 *
 * This module handles insight-related functionality for admins
 */
const express = require('express');
const router = express.Router();
const { validate } = require('@middlewares/validation.middleware');
const { authenticate } = require('@middlewares/auth.middleware');
const paginationMiddleware = require('@middlewares/pagination.middleware');
const insightController = require('./insight.controller');
const insightValidation = require('./insight.validation');

/**
 * Register routes
 */
function registerRoutes() {
  // Get all insights
  router.get(
    '/',
    authenticate,
    paginationMiddleware,
    validate(insightValidation.getAll),
    insightController.getAllInsights
  );

  // Get insight reports (must be before /:id route)
  router.get(
    '/reports',
    authenticate,
    paginationMiddleware,
    validate(insightValidation.getAll),
    insightController.getInsightReports
  );

  // Get contribution reports (must be before /:id route)
  router.get(
    '/contribution-reports',
    authenticate,
    paginationMiddleware,
    validate(insightValidation.getAll),
    insightController.getContributionReports
  );

  // Delete a contribution (admin only) (must be before /:id route)
  router.delete(
    '/contribution/:contributionId',
    authenticate,
    validate(insightValidation.deleteContribution),
    insightController.deleteContribution
  );

  // Get an insight by ID
  router.get(
    '/:id',
    authenticate,
    validate(insightValidation.getById),
    insightController.getInsightById
  );

  // Update an insight
  router.put(
    '/:id',
    authenticate,
    validate(insightValidation.update),
    insightController.updateInsight
  );

  // Delete an insight
  router.delete(
    '/:id',
    authenticate,
    validate(insightValidation.delete),
    insightController.deleteInsight
  );

  // Update insight status (approve or reject)
  router.patch(
    '/:id',
    authenticate,
    validate(insightValidation.updateStatus),
    insightController.updateStatus
  );

  return router;
}

// Export the router
module.exports = registerRoutes();
