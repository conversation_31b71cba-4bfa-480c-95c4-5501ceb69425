openapi: 3.0.0
info:
  title: WTD Platform User Authentication API
  version: 1.0.0
  description: Authentication endpoints for WTD Platform users

paths:
  /user/register:
    post:
      tags:
        - User
      summary: User Registration
      description: Register a new user
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UserRegisterRequest'
      responses:
        '201':
          description: Successfully registered
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AuthResponse'
        '422':
          description: Validation error
        '409':
          description: Email already exists

  /user/login:
    post:
      tags:
        - User
      summary: User Login
      description: Authenticate a user
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UserLoginRequest'
      responses:
        '200':
          description: Successfully authenticated
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AuthResponse'
        '401':
          description: Invalid credentials
        '422':
          description: Validation error

  /user/profile:
    get:
      tags:
        - User
      summary: Get User Profile
      description: Get current user's profile details
      security:
        - BearerAuth: []
      responses:
        '200':
          description: Successfully retrieved profile
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ProfileResponse'
        '401':
          description: Unauthorized

    put:
      tags:
        - User
      summary: Update Profile
      description: Update current user's profile
      security:
        - BearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateProfileRequest'
      responses:
        '200':
          description: Profile updated successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ProfileResponse'
        '400':
          description: Invalid input
        '401':
          description: Unauthorized

  /user/change-password:
    patch:
      tags:
        - User
      summary: Change Password
      description: Change the current user's password
      security:
        - BearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ChangePasswordRequest'
      responses:
        '200':
          description: Password changed successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SuccessResponse'
        '401':
          description: Unauthorized or invalid current password
        '422':
          description: Validation error

components:
  securitySchemes:
    BearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
  schemas:
    UserRegisterRequest:
      type: object
      required:
        - email
        - password
        - firstName
        - lastName
        - userType
        - focus
        - state
        - country
      properties:
        email:
          type: string
          format: email
          description: User's email address
          example: <EMAIL>
        password:
          type: string
          format: password
          minLength: 6
          description: User's password (minimum 6 characters)
          example: SecurePass123!
        firstName:
          type: string
          description: User's first name
          example: John
        lastName:
          type: string
          description: User's last name
          example: Doe
        userType:
          type: string
          enum: [EDUCATOR, EDUCATOR_PLUS, PROVIDER_PLUS]
          description: Type of user
          example: EDUCATOR
        position:
          type: string
          description: User's position (required for EDUCATOR and EDUCATOR_PLUS)
          example: Mathematics Teacher
        focus:
          type: array
          items:
            type: string
            format: uuid
          description: Array of focus area IDs
          example: ["3fa85f64-5717-4562-b3fc-2c963f66afa6"]
        state:
          type: string
          description: User's state
          example: California
        country:
          type: string
          description: User's country
          example: United States
        companyName:
          type: string
          description: Company name (required for PROVIDER_PLUS)
          example: Sunshine High School
      example:
        email: <EMAIL>
        password: SecurePass123!
        firstName: John
        lastName: Doe
        userType: EDUCATOR
        position: Mathematics Teacher
        focus: ["3fa85f64-5717-4562-b3fc-2c963f66afa6"]
        state: California
        country: United States
        companyName: Sunshine High School

    UserLoginRequest:
      type: object
      required:
        - email
        - password
      properties:
        email:
          type: string
          format: email
        password:
          type: string
          format: password

    AuthResponse:
      type: object
      properties:
        token:
          type: string
        user:
          $ref: '#/components/schemas/User'

    User:
      type: object
      properties:
        id:
          type: integer
        email:
          type: string
        firstName:
          type: string
        lastName:
          type: string
        role:
          type: string
          enum: [user]
        createdAt:
          type: string
          format: date-time
        updatedAt:
          type: string
          format: date-time

    ProfileResponse:
      type: object
      properties:
        status:
          type: string
        message:
          type: string
        data:
          $ref: '#/components/schemas/UserProfile'

    UserProfile:
      type: object
      properties:
        profilePic:
          type: string
          format: uri
          nullable: true
        firstName:
          type: string
        lastName:
          type: string
        userType:
          type: string
          enum: [EDUCATOR, EDUCATOR_PLUS, PROVIDER_PLUS]
        currentMilestone:
          type: object
          nullable: true
        credential:
          type: object
          properties:
            companyName:
              type: string
              nullable: true
            website:
              type: string
              format: uri
              nullable: true
            focus:
              type: array
              items:
                type: object
                properties:
                  id:
                    type: string
                    format: uuid
                  name:
                    type: string
            state:
              type: string
              nullable: true
            country:
              type: string
              nullable: true
            description:
              type: string
              nullable: true
            isPrivate:
              type: boolean
        departmentImpact:
          type: object
          properties:
            currentMilestone:
              type: object
              nullable: true
            contributions:
              type: integer
            followers:
              type: integer
            insightsShared:
              type: integer
            experiencesCreated:
              type: integer
            isPrivate:
              type: boolean
        iceBreakers:
          type: object
          nullable: true
          properties:
            teachSubject:
              type: string
              nullable: true
            contentIn3Words:
              type: string
              nullable: true
            inspiration:
              type: string
              nullable: true
            funFact:
              type: string
              nullable: true
            magicButton:
              type: string
              nullable: true
            resourcesEmojis:
              type: string
              nullable: true
            themeSong:
              type: string
              nullable: true
            impactTeacher:
              type: string
              nullable: true
            isPrivate:
              type: boolean
              nullable: true

    UpdateProfileRequest:
      type: object
      properties:
        firstName:
          type: string
        lastName:
          type: string
        profilePic:
          type: string
          format: uri
        credential:
          type: object
          properties:
            companyName:
              type: string
            website:
              type: string
              format: uri
            state:
              type: string
            country:
              type: string
            description:
              type: string
            isPrivate:
              type: boolean
            focus:
              type: array
              items:
                type: string
                format: uuid
        departmentImpact:
          type: object
          properties:
            isPrivate:
              type: boolean
        iceBreakers:
          type: object
          properties:
            teachSubject:
              type: string
            contentIn3Words:
              type: string
            inspiration:
              type: string
            funFact:
              type: string
            magicButton:
              type: string
            resourcesEmojis:
              type: string
            themeSong:
              type: string
            impactTeacher:
              type: string
            isPrivate:
              type: boolean
      example:
        firstName: "Sarah"
        lastName: "Johnson"
        profilePic: "https://example.com/profile-pics/sarah-johnson.jpg"
        credential:
          companyName: "Sunshine High School"
          website: "https://sunshinehigh.edu"
          state: "California"
          country: "United States"
          description: "Experienced mathematics teacher with a passion for innovative teaching methods"
          isPrivate: false
          focus: ["3fa85f64-5717-4562-b3fc-2c963f66afa6", "4fa85f64-5717-4562-b3fc-2c963f66afa7"]
        departmentImpact:
          isPrivate: false
        iceBreakers:
          teachSubject: "Mathematics and Statistics"
          contentIn3Words: "Numbers, Logic, Discovery"
          inspiration: "Helping students discover the beauty of mathematics"
          funFact: "I can solve a Rubik's cube in under 2 minutes"
          magicButton: "Instant understanding of complex concepts"
          resourcesEmojis: "📚📐🧮"
          themeSong: "We Will Rock You"
          impactTeacher: "Making math accessible and fun for everyone"
          isPrivate: false

    ChangePasswordRequest:
      type: object
      required:
        - currentPassword
        - newPassword
      properties:
        currentPassword:
          type: string
          format: password
          description: Current password
          example: "currentPassword123"
        newPassword:
          type: string
          format: password
          description: New password (minimum 6 characters)
          example: "newPassword123"
      example:
        currentPassword: "currentPassword123"
        newPassword: "newPassword123"

    SuccessResponse:
      type: object
      properties:
        success:
          type: boolean
          example: true
        message:
          type: string
          example: "Password changed successfully"