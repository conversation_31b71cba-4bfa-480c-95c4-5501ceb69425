/**
 * User Auth Validation Schemas
 *
 * Defines validation schemas for user authentication-related requests
 */
const { body } = require('express-validator');
const { VALIDATION } = require('@utils/messages.utils');
const { UserType } = require('@utils/enums.utils');

/**
 * User auth validation schemas
 */
const authValidation = {
  /**
   * Register validation schema
   */
  register: [
    // Common fields for all user types
    body('firstName').notEmpty().withMessage(VALIDATION.FIRST_NAME_REQUIRED),
    body('lastName').notEmpty().withMessage(VALIDATION.LAST_NAME_REQUIRED),
    body('email')
      .isEmail()
      .withMessage(VALIDATION.EMAIL_INVALID)
      .normalizeEmail()
      .toLowerCase(),
    body('password')
      .isLength({ min: 6 })
      .withMessage(VALIDATION.PASSWORD_LENGTH),
    body('userType')
      .isIn(UserType.values)
      .withMessage(VALIDATION.INVALID_USER_TYPE),
    body('focus').isArray().withMessage(VALIDATION.FOCUS_MUST_BE_ARRAY),
    body('focus.*').isUUID().withMessage(VALIDATION.INVALID_FOCUS_ID),
    body('state').notEmpty().withMessage(VALIDATION.STATE_REQUIRED),
    body('country').notEmpty().withMessage(VALIDATION.COUNTRY_REQUIRED),

    // Position required only for EDUCATOR and EDUCATOR_PLUS
    body('position')
      .if(body('userType').isIn([UserType.EDUCATOR, UserType.EDUCATOR_PLUS]))
      .notEmpty()
      .withMessage(VALIDATION.POSITION_REQUIRED),

    // Provider Plus specific fields
    body('companyName')
      .if(body('userType').equals(UserType.PROVIDER_PLUS))
      .notEmpty()
      .withMessage(VALIDATION.COMPANY_NAME_REQUIRED),
  ],

  /**
   * Login validation schema
   */
  login: [
    body('email')
      .isEmail()
      .withMessage(VALIDATION.EMAIL_INVALID)
      .normalizeEmail()
      .toLowerCase(),
    body('password').notEmpty().withMessage(VALIDATION.REQUIRED),
  ],

  /**
   * Update profile validation schema
   */
  updateProfile: [
    body('firstName')
      .optional()
      .notEmpty()
      .withMessage(VALIDATION.FIRST_NAME_REQUIRED),
    body('lastName')
      .optional()
      .notEmpty()
      .withMessage(VALIDATION.LAST_NAME_REQUIRED),
    body('profilePic').optional().isURL().withMessage(VALIDATION.INVALID_URL),

    // Credential validation
    body('credential.companyName').optional(),
    body('credential.website')
      .optional()
      .isURL()
      .withMessage(VALIDATION.INVALID_URL),
    body('credential.state').optional(),
    body('credential.country').optional(),
    body('credential.description').optional(),
    body('credential.isPrivate')
      .optional()
      .isBoolean()
      .withMessage(VALIDATION.INVALID_BOOLEAN),
    body('credential.focus')
      .optional()
      .isArray()
      .withMessage(VALIDATION.FOCUS_MUST_BE_ARRAY),
    body('credential.focus.*')
      .optional()
      .isUUID()
      .withMessage(VALIDATION.INVALID_FOCUS_ID),

    // Department impact validation
    body('departmentImpact.isPrivate')
      .optional()
      .isBoolean()
      .withMessage(VALIDATION.INVALID_BOOLEAN),

    // Ice breaker validation
    body('iceBreakers.teachSubject').optional(),
    body('iceBreakers.contentIn3Words').optional(),
    body('iceBreakers.inspiration').optional(),
    body('iceBreakers.funFact').optional(),
    body('iceBreakers.magicButton').optional(),
    body('iceBreakers.resourcesEmojis').optional(),
    body('iceBreakers.themeSong').optional(),
    body('iceBreakers.impactTeacher').optional(),
    body('iceBreakers.isPrivate')
      .optional()
      .isBoolean()
      .withMessage(VALIDATION.INVALID_BOOLEAN),
  ],

  /**
   * Change password validation schema
   */
  changePassword: [
    body('currentPassword')
      .notEmpty()
      .withMessage(VALIDATION.CURRENT_PASSWORD_REQUIRED),
    body('newPassword')
      .isLength({ min: 6 })
      .withMessage(VALIDATION.PASSWORD_LENGTH)
      .custom((value, { req }) => {
        if (value === req.body.currentPassword) {
          throw new Error(VALIDATION.NEW_PASSWORD_SAME_AS_CURRENT);
        }
        return true;
      }),
  ],
};

module.exports = authValidation;
