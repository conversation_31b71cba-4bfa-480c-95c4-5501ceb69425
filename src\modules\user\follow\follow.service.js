/**
 * Follow Service
 *
 * Handles business logic for user following functionality
 */
const followRepository = require('@models/repositories/follow.repository');

/**
 * Follow Service
 */
const followService = {
  /**
   * Toggle follow status for a user
   * @param {string} followerId - User ID of the follower
   * @param {string} followingId - User ID of the user being followed
   * @returns {Promise<Object>} Object with isFollowing status
   */
  toggleFollow: async (followerId, followingId) => {
    try {
      return await followRepository.toggleFollow(followerId, followingId);
    } catch (error) {
      throw error;
    }
  },

  /**
   * Get all followers for a user
   * @param {string} userId - User ID
   * @param {Object} options - Query options
   * @param {number} options.page - Page number
   * @param {number} options.limit - Items per page
   * @returns {Promise<Object>} Followers and pagination info
   */
  getFollowers: async (userId, { page, limit }) => {
    try {
      return await followRepository.getFollowers(userId, {
        page,
        limit,
      });
    } catch (error) {
      throw error;
    }
  },

  /**
   * Get all users that a user is following
   * @param {string} userId - User ID
   * @param {Object} options - Query options
   * @param {number} options.page - Page number
   * @param {number} options.limit - Items per page
   * @returns {Promise<Object>} Following users and pagination info
   */
  getFollowing: async (userId, { page, limit }) => {
    try {
      return await followRepository.getFollowing(userId, {
        page,
        limit,
      });
    } catch (error) {
      throw error;
    }
  },
};

module.exports = followService;
