/**
 * OnboardingConfig Service
 *
 * Handles onboarding config-related business logic
 */
const onboardingConfigRepository = require('@models/repositories/onboarding-config.repository');

/**
 * OnboardingConfig service
 */
const onboardingConfigService = {
  /**
   * Get all onboarding configs with pagination
   * @param {Object} options - Query options
   * @param {number} options.page - Page number
   * @param {number} options.limit - Items per page
   * @returns {Promise<Object>} Onboarding configs and pagination info
   */
  getAllConfigs: async ({ page, limit } = {}) => {
    try {
      return await onboardingConfigRepository.findAll({
        page,
        limit,
      });
    } catch (error) {
      console.error('Error in getAllConfigs service:', error);
      throw error;
    }
  },

  /**
   * Get onboarding config by ID
   * @param {string} id - Onboarding config ID
   * @returns {Promise<Object>} Onboarding config
   */
  getConfigById: async (id) => {
    try {
      return await onboardingConfigRepository.findById(id);
    } catch (error) {
      console.error('Error in getConfigById service:', error);
      throw error;
    }
  },

  /**
   * Create a new onboarding config
   * @param {Object} data - Onboarding config data
   * @returns {Promise<Object>} Created onboarding config
   */
  createConfig: async (data) => {
    try {
      return await onboardingConfigRepository.create(data);
    } catch (error) {
      console.error('Error in createConfig service:', error);
      throw error;
    }
  },

  /**
   * Update onboarding config
   * @param {string} id - Onboarding config ID
   * @param {Object} data - Data to update
   * @returns {Promise<Object>} Updated onboarding config
   */
  updateConfig: async (id, data) => {
    try {
      return await onboardingConfigRepository.update(id, data);
    } catch (error) {
      console.error('Error in updateConfig service:', error);
      throw error;
    }
  },

  /**
   * Delete onboarding config
   * @param {string} id - Onboarding config ID
   * @returns {Promise<boolean>} True if deleted
   */
  deleteConfig: async (id) => {
    try {
      return await onboardingConfigRepository.delete(id);
    } catch (error) {
      console.error('Error in deleteConfig service:', error);
      throw error;
    }
  },
};

module.exports = onboardingConfigService;
