openapi: 3.0.0
info:
  title: WTD Platform User Follow API
  version: 1.0.0
  description: API endpoints for managing user follow relationships

paths:
  /user/follow/{followingId}:
    post:
      tags:
        - User Follow
      summary: Toggle Follow Status
      description: Follow or unfollow a user
      security:
        - BearerAuth: []
      parameters:
        - name: followingId
          in: path
          required: true
          schema:
            type: string
            format: uuid
          description: ID of the user to follow/unfollow
      responses:
        '200':
          description: Successfully toggled follow status
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/FollowResponse'
        '400':
          description: Bad Request - Cannot follow yourself
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized - User is not authenticated
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: User not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

components:
  securitySchemes:
    BearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT

  schemas:
    FollowResponse:
      type: object
      properties:
        status:
          type: integer
          example: 200
        message:
          type: string
          example: "Follow status toggled successfully"
        data:
          type: object
          properties:
            isFollowing:
              type: boolean
              description: Whether the current user is following the target user
              example: true

    ErrorResponse:
      type: object
      properties:
        status:
          type: integer
          example: 400
        message:
          type: string
          example: "Cannot follow yourself" 