openapi: 3.0.0
info:
  title: WTD Platform User Follow API
  version: 1.0.0
  description: API endpoints for managing user follow relationships

paths:
  /user/follow/{followingId}:
    post:
      tags:
        - User Follow
      summary: Toggle Follow Status
      description: Follow or unfollow a user
      security:
        - BearerAuth: []
      parameters:
        - name: followingId
          in: path
          required: true
          schema:
            type: string
            format: uuid
          description: ID of the user to follow/unfollow
      responses:
        '200':
          description: Successfully toggled follow status
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/FollowResponse'
        '400':
          description: Bad Request - Cannot follow yourself
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized - User is not authenticated
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: User not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

components:
  securitySchemes:
    BearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT

  schemas:
    FollowResponse:
      type: object
      properties:
        status:
          type: integer
          example: 200
        message:
          type: string
          example: "Follow status toggled successfully"
        data:
          type: object
          properties:
            isFollowing:
              type: boolean
              description: Whether the current user is following the target user
              example: true
            follower:
              $ref: '#/components/schemas/User'
            following:
              $ref: '#/components/schemas/User'

    User:
      type: object
      properties:
        id:
          type: string
          format: uuid
          description: Unique identifier for the user
          example: "123e4567-e89b-12d3-a456-************"
        firstName:
          type: string
          description: User's first name
          example: "John"
        lastName:
          type: string
          description: User's last name
          example: "Doe"
        email:
          type: string
          format: email
          description: User's email address
          example: "<EMAIL>"
        profileImage:
          type: string
          description: URL of the user's profile image
          example: "https://example.com/profile.jpg"
        followersCount:
          type: integer
          description: Number of followers
          example: 10
        followingCount:
          type: integer
          description: Number of users being followed
          example: 5
        createdAt:
          type: string
          format: date-time
          description: Date and time when the user was created
          example: "2023-01-01T00:00:00.000Z"
        updatedAt:
          type: string
          format: date-time
          description: Date and time when the user was last updated
          example: "2023-01-01T00:00:00.000Z"

    ErrorResponse:
      type: object
      properties:
        status:
          type: integer
          example: 400
        message:
          type: string
          example: "Cannot follow yourself" 