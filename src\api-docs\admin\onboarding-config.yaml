openapi: 3.0.0
info:
  title: WTD Platform Onboarding Config API
  version: 1.0.0
  description: API endpoints for managing onboarding screen configurations

paths:
  /admin/onboarding-config:
    get:
      tags:
        - Onboarding Config
      summary: List All Onboarding Configs
      description: Get a list of all onboarding configs with pagination
      security:
        - BearerAuth: []
      parameters:
        - $ref: '#/components/parameters/PageParam'
        - $ref: '#/components/parameters/LimitParam'
      responses:
        '200':
          description: Successfully retrieved onboarding configs
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/OnboardingConfigListResponse'
    post:
      tags:
        - Onboarding Config
      deprecated: true
      summary: Create Onboarding Config
      description: Create a new onboarding screen configuration
      security:
        - BearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateOnboardingConfigRequest'
      responses:
        '201':
          description: Successfully created onboarding config
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/OnboardingConfigResponse'

  /admin/onboarding-config/{id}:
    get:
      tags:
        - Onboarding Config
      summary: Get Onboarding Config by ID
      description: Get a specific onboarding config by its ID
      security:
        - BearerAuth: []
      parameters:
        - $ref: '#/components/parameters/OnboardingConfigIdParam'
      responses:
        '200':
          description: Successfully retrieved onboarding config
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/OnboardingConfigResponse'
        '404':
          description: Onboarding config not found
    put:
      tags:
        - Onboarding Config
      summary: Update Onboarding Config
      description: Update an existing onboarding config
      security:
        - BearerAuth: []
      parameters:
        - $ref: '#/components/parameters/OnboardingConfigIdParam'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateOnboardingConfigRequest'
      responses:
        '200':
          description: Successfully updated onboarding config
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/OnboardingConfigResponse'
        '404':
          description: Onboarding config not found
    delete:
      tags:
        - Onboarding Config
      deprecated: true
      summary: Delete Onboarding Config
      description: Delete an existing onboarding config
      security:
        - BearerAuth: []
      parameters:
        - $ref: '#/components/parameters/OnboardingConfigIdParam'
      responses:
        '200':
          description: Successfully deleted onboarding config
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DeleteResponse'
        '404':
          description: Onboarding config not found

components:
  securitySchemes:
    BearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT

  parameters:
    PageParam:
      name: page
      in: query
      schema:
        type: integer
        minimum: 1
        default: 1
      description: Page number (1-based)

    LimitParam:
      name: limit
      in: query
      schema:
        type: integer
        minimum: 1
        maximum: 100
        default: 10
      description: Number of items per page

    OnboardingConfigIdParam:
      name: id
      in: path
      required: true
      schema:
        type: string
        format: uuid
      description: Onboarding config ID

  schemas:
    OnboardingConfig:
      type: object
      properties:
        id:
          type: string
          format: uuid
          description: Unique identifier for the onboarding config
        title:
          type: string
          description: Title of the onboarding screen
        description:
          type: string
          description: Description text for the onboarding screen
        imageUrl:
          type: string
          description: URL of the image to display
        createdAt:
          type: string
          format: date-time
          description: Date and time when the config was created
        updatedAt:
          type: string
          format: date-time
          description: Date and time when the config was last updated
      required:
        - id
        - createdAt
        - updatedAt
      example:
        id: "123e4567-e89b-12d3-a456-************"
        title: "Welcome!"
        description: "Welcome to WTD Platform"
        imageUrl: "https://example.com/welcome.jpg"
        createdAt: "2023-01-01T00:00:00.000Z"
        updatedAt: "2023-01-01T00:00:00.000Z"

    CreateOnboardingConfigRequest:
      type: object
      properties:
        title:
          type: string
          description: Title of the onboarding screen
          example: "Welcome!"
        description:
          type: string
          description: Description text for the onboarding screen
          example: "Welcome to WTD Platform"
        imageUrl:
          type: string
          description: URL of the image to display
          example: "https://example.com/welcome.jpg"

    UpdateOnboardingConfigRequest:
      type: object
      properties:
        title:
          type: string
          description: Updated title of the onboarding screen
          example: "Welcome to WTD!"
        description:
          type: string
          description: Updated description text for the onboarding screen
          example: "Welcome to the WTD Platform"
        imageUrl:
          type: string
          description: Updated URL of the image to display
          example: "https://example.com/new-welcome.jpg"

    OnboardingConfigResponse:
      type: object
      properties:
        success:
          type: boolean
          example: true
        message:
          type: string
          example: "Onboarding config created successfully"
        data:
          $ref: '#/components/schemas/OnboardingConfig'

    OnboardingConfigListResponse:
      type: object
      properties:
        success:
          type: boolean
          example: true
        message:
          type: string
          example: "Onboarding configs retrieved successfully"
        data:
          type: array
          items:
            $ref: '#/components/schemas/OnboardingConfig'
        pagination:
          $ref: '#/components/schemas/Pagination'

    DeleteResponse:
      type: object
      properties:
        success:
          type: boolean
          example: true
        message:
          type: string
          example: "Onboarding config deleted successfully"

    Pagination:
      type: object
      properties:
        total:
          type: integer
          description: Total number of items
          example: 12
        page:
          type: integer
          description: Current page number
          example: 1
        limit:
          type: integer
          description: Number of items per page
          example: 10
        totalPages:
          type: integer
          description: Total number of pages
          example: 2
        hasNext:
          type: boolean
          description: Whether there is a next page
          example: true
        hasPrevious:
          type: boolean
          description: Whether there is a previous page
          example: false 