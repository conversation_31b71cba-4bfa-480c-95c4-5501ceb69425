/**
 * Follow Validation Schemas
 *
 * Defines validation schemas for follow-related requests
 */
const { param, query } = require('express-validator');
const { VALIDATION } = require('@utils/messages.utils');

/**
 * Follow validation schemas
 */
const followValidation = {
  /**
   * Toggle follow validation schema
   */
  toggleFollow: [
    param('followingId').isUUID().withMessage(VALIDATION.INVALID_FORMAT),
  ],
};

module.exports = followValidation;
