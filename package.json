{"name": "wtd-platform", "version": "1.0.0", "description": "WTD Platform API", "main": "main.js", "license": "MIT", "scripts": {"start": "node main.js", "dev": "node main.js", "local": "node main.js", "lint": "eslint src/ main.js --fix --cache", "prettier": "prettier --config ./.prettierrc \"src/**/*.{js,json}\" \"main.js\" --write", "format": "yarn run prettier && yarn run lint", "db:migrate": "npx sequelize-cli db:migrate", "db:migrate:undo": "npx sequelize-cli db:migrate:undo", "db:migrate:generate": "npx sequelize-cli migration:generate --name", "db:model:generate": "npx sequelize-cli model:generate", "db:seed": "npx sequelize-cli db:seed:all", "db:seed:undo": "npx sequelize-cli db:seed:undo", "db:seed:generate": "npx sequelize-cli seed:generate --name"}, "husky": {"hooks": {"pre-commit": "yarn run format"}}, "dependencies": {"axios": "^1.9.0", "bcrypt": "^5.1.1", "body-parser": "^1.20.2", "chalk": "^4.1.2", "compression": "^1.8.0", "cors": "^2.8.5", "dotenv": "^16.5.0", "errorhandler": "^1.5.1", "express": "^4.19.2", "express-basic-auth": "^1.2.1", "express-validator": "^7.2.1", "jsonwebtoken": "^9.0.2", "lodash": "4.17.21", "lusca": "^1.7.0", "module-alias": "2.2.3", "morgan": "^1.10.0", "pg": "^8.16.0", "sequelize": "^6.37.7", "swagger-jsdoc": "^6.2.8", "swagger-ui": "^5.21.0", "swagger-ui-express": "^5.0.1", "uuid": "11.1.0"}, "devDependencies": {"chai": "^4.3.7", "cross-env": "7.0.3", "eslint": "^9.27.0", "eslint-config-airbnb-base": "^15.0.0", "eslint-config-prettier": "^10.1.5", "eslint-plugin-import": "^2.31.0", "eslint-plugin-prettier": "^5.4.0", "husky": "^9.1.7", "mocha": "^10.2.0", "nyc": "^15.1.0", "prettier": "^3.5.3", "prettier-eslint": "^16.4.2", "sequelize-cli": "^6.6.2", "sinon": "^15.2.0", "supertest": "^6.3.3"}, "resolutions": {"chalk": "4.1.2"}, "engines": {"node": ">=20.12.1", "yarn": ">=1.22.22"}, "_moduleAliases": {"@root": ".", "@src": "src", "@modules": "src/modules", "@admin": "src/modules/admin", "@user": "src/modules/user", "@utils": "src/utils", "@config": "src/config", "@middlewares": "src/middlewares", "@models": "src/models", "@repositories": "src/models/repositories", "@api": "src/api-docs"}}