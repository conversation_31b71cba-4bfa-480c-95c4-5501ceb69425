/**
 * ExperienceReview Model
 * Represents a user's review for an experience
 */
const { Model, DataTypes } = require('sequelize');

class ExperienceReview extends Model {
  static init(sequelize) {
    super.init(
      {
        id: {
          type: DataTypes.UUID,
          defaultValue: DataTypes.UUIDV4,
          primaryKey: true,
        },
        experienceId: {
          type: DataTypes.UUID,
          allowNull: false,
          references: {
            model: 'Experience',
            key: 'id',
          },
          onDelete: 'CASCADE',
        },
        userId: {
          type: DataTypes.UUID,
          allowNull: false,
          references: {
            model: 'User',
            key: 'id',
          },
          onDelete: 'CASCADE',
        },
        courseRating: {
          type: DataTypes.INTEGER,
          allowNull: false,
          validate: {
            min: 1,
            max: 5,
          },
        },
        providerRating: {
          type: DataTypes.INTEGER,
          allowNull: false,
          validate: {
            min: 1,
            max: 5,
          },
        },
        reviewText: {
          type: DataTypes.TEXT,
          allowNull: true,
        },
      },
      {
        sequelize,
        modelName: 'ExperienceReview',
        tableName: 'ExperienceReview',
        timestamps: true,
        indexes: [
          {
            fields: ['experienceId'],
            name: 'experience_review_experience_id_idx',
          },
          {
            fields: ['userId'],
            name: 'experience_review_user_id_idx',
          },
          {
            unique: true,
            fields: ['experienceId', 'userId'],
            name: 'experience_review_unique_idx',
          },
        ],
      }
    );
  }

  static associate(models) {
    this.belongsTo(models.Experience, {
      foreignKey: 'experienceId',
      as: 'experience',
      onDelete: 'CASCADE',
    });

    this.belongsTo(models.User, {
      foreignKey: 'userId',
      as: 'user',
      onDelete: 'CASCADE',
    });
  }
}

module.exports = ExperienceReview;
