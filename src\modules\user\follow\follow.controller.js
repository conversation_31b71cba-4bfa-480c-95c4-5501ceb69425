/**
 * Follow Controller
 *
 * Handles HTTP requests for user following functionality
 */
const followService = require('./follow.service');
const { ApiResponse } = require('@utils/response.utils');
const { FOLLOW } = require('@utils/messages.utils');

/**
 * Follow Controller
 */
const followController = {
  /**
   * Toggle follow status for a user
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next middleware function
   * @returns {Promise<void>}
   */
  toggleFollow: async (req, res, next) => {
    try {
      const { followingId } = req.params;
      const { id: followerId } = req.user;

      const result = await followService.toggleFollow(followerId, followingId);

      return ApiResponse.success(
        res,
        result.isFollowing ? FOLLOW.FOLLOWED : FOLLOW.UNFOLLOWED,
        result
      );
    } catch (error) {
      next(error);
    }
  },
};

module.exports = followController;
