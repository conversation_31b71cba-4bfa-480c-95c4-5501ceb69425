openapi: 3.0.0
info:
  title: WTD Platform User Focus Areas API
  version: 1.0.0
  description: API endpoints for accessing Focus areas as a user

paths:
  /user/focus:
    get:
      tags:
        - User Focus Areas
      summary: List All Focus Areas
      description: Get a list of all focus areas with pagination and optional search
      parameters:
        - $ref: '#/components/parameters/PageParam'
        - $ref: '#/components/parameters/LimitParam'
        - $ref: '#/components/parameters/SearchParam'
      responses:
        '200':
          description: Successfully retrieved focus areas
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/FocusListResponse'
        '401':
          description: Unauthorized - User is not authenticated

  /user/focus/{id}:
    get:
      tags:
        - User Focus Areas
      summary: Get Focus Area by ID
      description: Get a specific focus area by its ID
      parameters:
        - $ref: '#/components/parameters/FocusIdParam'
      responses:
        '200':
          description: Successfully retrieved focus area
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/FocusResponse'
        '401':
          description: Unauthorized - User is not authenticated
        '404':
          description: Focus area not found

components:
  securitySchemes:
    BearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT

  parameters:
    PageParam:
      name: page
      in: query
      schema:
        type: integer
        minimum: 1
        default: 1
      description: Page number (1-based)

    LimitParam:
      name: limit
      in: query
      schema:
        type: integer
        minimum: 1
        maximum: 100
        default: 10
      description: Number of items per page

    SearchParam:
      name: search
      in: query
      schema:
        type: string
      description: Optional search term to filter focus areas by name

    FocusIdParam:
      name: id
      in: path
      required: true
      schema:
        type: string
        format: uuid
      description: Focus area ID
  schemas:
    Focus:
      type: object
      properties:
        id:
          type: string
          format: uuid
          description: Unique identifier for the focus area
        name:
          type: string
          description: Name of the focus area
        createdAt:
          type: string
          format: date-time
          description: Date and time when the focus area was created
        updatedAt:
          type: string
          format: date-time
          description: Date and time when the focus area was last updated
      required:
        - id
        - name
        - createdAt
        - updatedAt
      example:
        id: "123e4567-e89b-12d3-a456-************"
        name: "Elementary"
        createdAt: "2023-01-01T00:00:00.000Z"
        updatedAt: "2023-01-01T00:00:00.000Z"

    FocusResponse:
      type: object
      properties:
        status:
          type: integer
          example: 200
        message:
          type: string
          example: "Focus area retrieved successfully"
        data:
          $ref: '#/components/schemas/Focus'

    FocusListResponse:
      type: object
      properties:
        status:
          type: integer
          example: 200
        message:
          type: string
          example: "All focus areas retrieved successfully"
        data:
          type: array
          items:
            $ref: '#/components/schemas/Focus'
        pagination:
          $ref: '#/components/schemas/Pagination'

    Pagination:
      type: object
      properties:
        total:
          type: integer
          description: Total number of items
          example: 3
        page:
          type: integer
          description: Current page number
          example: 1
        limit:
          type: integer
          description: Number of items per page
          example: 10
        totalPages:
          type: integer
          description: Total number of pages
          example: 1
        hasNext:
          type: boolean
          description: Whether there is a next page
          example: false
        hasPrevious:
          type: boolean
          description: Whether there is a previous page
          example: false
