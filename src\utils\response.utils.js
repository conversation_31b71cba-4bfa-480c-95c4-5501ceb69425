/**
 * API Response Utility
 *
 * Provides standardized response formatting
 */
const { HttpStatus } = require('./enums.utils');

/**
 * API Response formatter
 */
const ApiResponse = {
  /**
   * Format a success response
   * @param {Object} res - Express response object
   * @param {string} message - Success message
   * @param {*} data - Response data
   * @param {number|Object} statusCodeOrMeta - HTTP status code (default: 200) or metadata
   */
  success: (res, message, data = null, statusCodeOrMeta = HttpStatus.OK) => {
    let statusCode = HttpStatus.OK;
    let meta = null;

    // Check if the fourth parameter is a number (status code) or an object (metadata)
    if (typeof statusCodeOrMeta === 'number') {
      statusCode = statusCodeOrMeta;
    } else if (statusCodeOrMeta && typeof statusCodeOrMeta === 'object') {
      meta = statusCodeOrMeta;
    }

    const response = {
      status: statusCode,
      message,
    };

    if (data !== null) {
      response.data = data;
    }

    if (meta !== null) {
      // Add each metadata property to the response
      Object.keys(meta).forEach((key) => {
        if (key !== 'statusCode') {
          // Skip statusCode as it's already included
          response[key] = meta[key];
        }
      });
    }

    return res.status(statusCode).json(response);
  },

  /**
   * Format a created response (201)
   * @param {Object} res - Express response object
   * @param {string} message - Success message
   * @param {*} data - Response data
   */
  created: (res, message, data = null) => {
    return ApiResponse.success(res, message, data, HttpStatus.CREATED);
  },

  /**
   * Format an error response
   * @param {Object} res - Express response object
   * @param {string} message - Error message
   * @param {*} errors - Error details
   * @param {number} statusCode - HTTP status code (default: 400)
   */
  error: (res, message, errors = null, statusCode = HttpStatus.BAD_REQUEST) => {
    // Define response with proper type annotation
    const response = {
      status: statusCode,
      error: getErrorTitle(statusCode),
      message,
      errors: null, // Initialize with null
    };

    if (errors !== null) {
      response.errors = errors;
    }

    return res.status(statusCode).json(response);
  },
};

/**
 * Get a descriptive title for an error based on status code
 * @param {number} statusCode - HTTP status code
 * @returns {string} Error title
 */
function getErrorTitle(statusCode) {
  switch (statusCode) {
    case HttpStatus.BAD_REQUEST:
      return 'Bad Request';
    case HttpStatus.UNAUTHORIZED:
      return 'Unauthorized';
    case HttpStatus.FORBIDDEN:
      return 'Forbidden';
    case HttpStatus.NOT_FOUND:
      return 'Not Found';
    case HttpStatus.CONFLICT:
      return 'Conflict';
    case HttpStatus.INTERNAL_SERVER_ERROR:
      return 'Internal Server Error';
    default:
      return 'Error';
  }
}

module.exports = {
  ApiResponse,
};
