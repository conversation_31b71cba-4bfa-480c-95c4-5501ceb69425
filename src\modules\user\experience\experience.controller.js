/**
 * Experience Controller
 *
 * Handles experience-related HTTP requests
 */
const experienceService = require('./services/experience.service');
const experienceEnrollmentService = require('./services/experience-enrollment.service');
const experienceReviewService = require('./services/experience-review.service');
const { ApiResponse } = require('@utils/response.utils');
const {
  EXPERIENCE,
  EXPERIENCE_ENROLLMENT,
  EXPERIENCE_REVIEW,
} = require('@utils/messages.utils');

/**
 * Experience controller
 */
const experienceController = {
  /**
   * Create a new experience
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  createExperience: async (req, res, next) => {
    try {
      const data = req.body;
      const userId = req.user.id;

      const experience = await experienceService.createExperience(data, userId);

      return ApiResponse.created(res, EXPERIENCE.CREATED, experience);
    } catch (error) {
      next(error);
    }
  },

  /**
   * Get all experiences with pagination
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  getAllExperiences: async (req, res, next) => {
    try {
      const { myExperiences } = req.query;
      const { page, limit } = req.pagination;
      const userId = req.user.id;

      const result = await experienceService.getAllExperiences({
        page,
        limit,
        createdBy: myExperiences === 'true' ? req.user.id : undefined,
        userId,
      });

      return ApiResponse.success(
        res,
        EXPERIENCE.ALL_RETRIEVED,
        result.experiences,
        {
          pagination: result.pagination,
        }
      );
    } catch (error) {
      next(error);
    }
  },

  /**
   * Get complete experience details
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  getExperienceById: async (req, res, next) => {
    try {
      const { experienceId } = req.params;
      const userId = req.user.id;

      const experience = await experienceService.getExperienceDetails(
        experienceId,
        userId
      );

      return ApiResponse.success(res, EXPERIENCE.RETRIEVED, experience);
    } catch (error) {
      next(error);
    }
  },

  /**
   * Enroll a user in an experience
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  enrollUser: async (req, res, next) => {
    try {
      const { experienceId } = req.params;
      const { startDate } = req.body;
      const userId = req.user.id;

      const enrollment = await experienceEnrollmentService.enrollUser(
        experienceId,
        userId,
        startDate
      );

      return ApiResponse.created(
        res,
        EXPERIENCE_ENROLLMENT.CREATED,
        enrollment
      );
    } catch (error) {
      next(error);
    }
  },

  /**
   * Update enrollment status
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  updateEnrollmentStatus: async (req, res, next) => {
    try {
      const { experienceId } = req.params;
      const { status } = req.body;
      const userId = req.user.id;

      const enrollment =
        await experienceEnrollmentService.updateEnrollmentStatus(
          experienceId,
          userId,
          status
        );

      return ApiResponse.success(
        res,
        EXPERIENCE_ENROLLMENT.STATUS_UPDATED,
        enrollment
      );
    } catch (error) {
      next(error);
    }
  },

  /**
   * Submit an experience review
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  submitExperienceReview: async (req, res, next) => {
    try {
      const { experienceId } = req.params;
      const { courseRating, providerRating, reviewText } = req.body;
      const userId = req.user.id;

      const review = await experienceReviewService.submitReview(
        experienceId,
        userId,
        {
          courseRating,
          providerRating,
          reviewText,
        }
      );

      return ApiResponse.created(res, EXPERIENCE_REVIEW.CREATED, review);
    } catch (error) {
      next(error);
    }
  },
};

module.exports = experienceController;
